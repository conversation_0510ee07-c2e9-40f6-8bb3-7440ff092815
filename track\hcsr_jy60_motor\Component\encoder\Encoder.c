#include "Encoder.h"

// 左右编码器电机
Encoder left_encoder;
Encoder right_encoder;

/**
 * @brief 初始化编码器应用
 */
void Encoder_Init(void)
{
	Encoder_Driver_Init(&left_encoder, &htim3, 0);	// 左编码器不反向
	Encoder_Driver_Init(&right_encoder, &htim1, 1); // 右编码器不反向
}

/**
 * @brief 编码器应用运行任务 (应由调度器周期性调用)
 */
void Encoder_Task(void)
{
	Encoder_Driver_Update(&left_encoder);
	Encoder_Driver_Update(&right_encoder);
//	my_printf(&huart6, "Left:%.2f Right:%.2f\r\n", left_encoder.speed_cm_s, right_encoder.speed_cm_s);
////	my_printf(&huart4, "Left:%.2f Right:%.2f\r\n", (left_encoder.speed_cm_s) * SAMPLING_TIME_S, (right_encoder.speed_cm_s) * SAMPLING_TIME_S);
}

/**
 * @brief 初始化编码器驱动
 */
void Encoder_Driver_Init(Encoder *encoder, TIM_HandleTypeDef *htim, unsigned char reverse)
{
	encoder->htim = htim;
	encoder->reverse = reverse;

	// 启动定时器的编码器模式
	HAL_TIM_Encoder_Start(encoder->htim, TIM_CHANNEL_ALL);

	// 清零计数器
	__HAL_TIM_SetCounter(encoder->htim, 0);

	// 初始化数据结构
	encoder->count = 0;
	encoder->total_count = 0;
	encoder->speed_cm_s = 0.0f;
}

/**
 * @brief 更新编码器数据 (应周期性调用, 例如10ms一次)
 */
void Encoder_Driver_Update(Encoder *encoder)
{
	// 1. 读取原始计数值
	encoder->count = (int16_t)__HAL_TIM_GetCounter(encoder->htim);

	// 2. 处理编码器反向
	encoder->count = encoder->reverse == 0 ? encoder->count : -encoder->count;

	// 3. 清零硬件计数器，为下个周期做准备
	__HAL_TIM_SetCounter(encoder->htim, 0);

	// 4. 累计总数
	encoder->total_count += encoder->count;

	// 5. 计算速度 (cm/s)
	// 速度 = (计数值 / PPR) * 周长 / 采样时间

	encoder->speed_cm_s = (float)encoder->count / ENCODER_PPR * WHEEL_CIRCUMFERENCE_CM / SAMPLING_TIME_S;
	encoder->local_cm = (float)encoder->total_count / ENCODER_PPR * WHEEL_CIRCUMFERENCE_CM ;
}
