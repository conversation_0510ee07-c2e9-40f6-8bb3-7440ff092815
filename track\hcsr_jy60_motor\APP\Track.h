#ifndef __TRACK_H__
#define __TRACK_H__

#include "mydefine.h"
#define TRACK_MODE_HARDWARE_IIC 0 // Ó²¼þIIC
#define TRACK_MODE_IO 1           // IOÄ£Ê½
#define TRACK_MODE_TYPE TRACK_MODE_HARDWARE_IIC 

#if TRACK_MODE_TYPE == TRACK_MODE_IO
extern uint8_t sensor1, sensor2, sensor3, sensor4, sensor5, sensor6;
#elif TRACK_MODE_TYPE == TRACK_MODE_HARDWARE_IIC
extern uint8_t Digital;
#endif

void Gray_Task(void);
void PrintTrackerStatus(void);
void read_line_sensors(void);
#endif
