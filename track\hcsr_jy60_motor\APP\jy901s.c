/**
 ******************************************************************************
 * @file    jy901s_driver.c
 * @brief   JY901S 九轴传感器驱动实现文件
 * <AUTHOR>
 * @date    2025-07-19
 ******************************************************************************
 * @attention
 *
 * 该驱动仅依赖用户提供的串口发送函数（my_printf），
 * 解析帧格式为 JY901S 输出的 0x55 + Type + Data + 校验和。
 * 仅示例解析 Z 轴陀螺仪角速度（0x52）和角度（0x53）。
 *
 ******************************************************************************
 */

#include "jy901s.h"

uint8_t jy901s_dma_rx_buffer[JY901S_DMA_BUFFER_SIZE];
/**
 * @brief 创建JY901S实例
 * @param jy: JY901S实体指针
 * @param huart: 串口句柄
 * @param timeout_ms: 超时时间
 * @retval 0: 成功, -1: 参数错误
 */
int8_t JY901S_Create(JY901S_t *jy, UART_HandleTypeDef *huart, uint32_t timeout_ms)
{
    if (jy == NULL || huart == NULL)
    {
        return -1;
    }

    jy->hw.huart = huart;
    jy->hw.timeout_ms = timeout_ms;
    jy->state = JY901S_STATE_IDLE;
    jy->enable = 1;
    jy->rx_index = 0;
    HAL_UART_Receive_DMA(huart, jy901s_dma_rx_buffer, JY901S_DMA_BUFFER_SIZE);

    return 0;
}

/**
 * @brief 处理接收数据缓冲区（状态机解析）
 * @param jy: JY901S实体指针
 * @param buffer: 接收数据缓冲区
 * @param length: 数据长度
 * @retval 0: 成功, -1: 参数错误
 */
int8_t JY901S_ProcessBuffer(JY901S_t *jy, uint8_t *buffer, uint16_t length)
{
    if (jy == NULL || buffer == NULL || length == 0)
    {
        return -1;
    }

    if (!jy->enable)
    {
        return -1;
    }

    for (uint16_t i = 0; i < length; i++)
    {
        uint8_t byte = buffer[i];

        switch (jy->state)
        {
        case JY901S_STATE_IDLE:
            if (byte == JY901S_HEADER)
            {
                jy->rx_buffer[0] = byte;
                jy->rx_index = 1;
                jy->state = JY901S_STATE_RECEIVING;
            }
            break;

        case JY901S_STATE_RECEIVING:
            jy->rx_buffer[jy->rx_index] = byte;
            jy->rx_index++;

            if (jy->rx_index >= JY901S_PACKET_SIZE)
            {
                uint8_t calculated = JY901S_CalculateChecksum(jy->rx_buffer, JY901S_PACKET_SIZE - 1);
                uint8_t received = jy->rx_buffer[JY901S_PACKET_SIZE - 1];

                if (calculated == received)
                {
                    uint8_t packet_type = jy->rx_buffer[1];

                    if (packet_type == JY901S_TYPE_GYRO)
                    {
                        JY901S_ParseGyroPacket(jy, jy->rx_buffer);
                    }
                    else if (packet_type == JY901S_TYPE_ANGLE)
                    {
                        JY901S_ParseAnglePacket(jy, jy->rx_buffer);
                    }

                    jy->state = JY901S_STATE_DATA_READY;
                }
                else
                {
                    jy->state = JY901S_STATE_ERROR;
                }

                jy->rx_index = 0;

                if (jy->state == JY901S_STATE_ERROR)
                {
                    jy->state = JY901S_STATE_IDLE;
                }
            }
            break;

        case JY901S_STATE_DATA_READY:
            // 数据帧已处理完毕，回到IDLE准备下一帧
            jy->state = JY901S_STATE_IDLE;
            i--; // 当前字节重新处理
            break;

        case JY901S_STATE_ERROR:
            // 错误后重新找包头
            jy->state = JY901S_STATE_IDLE;
            i--;
            break;

        default:
            jy->state = JY901S_STATE_IDLE;
            break;
        }
    }

    return 0;
}

/**
 * @brief 计算校验和
 * @param data: 数据指针
 * @param length: 长度（不包含校验和字节）
 * @retval 校验和
 */
uint8_t JY901S_CalculateChecksum(uint8_t *data, uint8_t length)
{
    uint8_t sum = 0;
    for (uint8_t i = 0; i < length; i++)
    {
        sum += data[i];
    }
    return sum;
}

/**
 * @brief 解析陀螺仪数据包
 */
void JY901S_ParseGyroPacket(JY901S_t *jy, uint8_t *packet)
{
    int16_t wz = (packet[6] << 8) | packet[5];
    jy->data.gyro_z_raw = wz / 32768.0f * 2000.0f; // 量程±2000°/s
    jy->data.data_valid = 1;
}

/**
 * @brief 解析角度数据包
 */
void JY901S_ParseAnglePacket(JY901S_t *jy, uint8_t *packet)
{
    int16_t yaw = (packet[6] << 8) | packet[5];
    jy->data.yaw = yaw / 32768.0f * 180.0f; // 量程±180°
    jy->data.data_valid = 1;
}

/**
 * @brief 获取角速度Z
 */
float JY901S_GetGyroZ(JY901S_t *jy)
{
    return (jy && jy->data.data_valid) ? jy->data.gyro_z_raw : 0.0f;
}

/**
 * @brief 获取偏航角
 */
float JY901S_GetYaw(JY901S_t *jy)
{
    return (jy && jy->data.data_valid) ? jy->data.yaw : 0.0f;
}
float yaw1;
void JY901S_Task(void)
{
    yaw1 = JY901S_GetYaw(&jy901s);
    my_printf(jy901s.hw.huart, "Yaw: %.2f deg\r\n", yaw1);
}