#MicroXplorer Configuration settings - do not modify
CAD.formats=
CAD.pinconfig=
CAD.provider=
Dma.Request0=UART4_RX
Dma.RequestsNb=1
Dma.UART4_RX.0.Direction=DMA_PERIPH_TO_MEMORY
Dma.UART4_RX.0.FIFOMode=DMA_FIFOMODE_DISABLE
Dma.UART4_RX.0.Instance=DMA1_Stream2
Dma.UART4_RX.0.MemDataAlignment=DMA_MDATAALIGN_BYTE
Dma.UART4_RX.0.MemInc=DMA_MINC_ENABLE
Dma.UART4_RX.0.Mode=DMA_NORMAL
Dma.UART4_RX.0.PeriphDataAlignment=DMA_PDATAALIGN_BYTE
Dma.UART4_RX.0.PeriphInc=DMA_PINC_DISABLE
Dma.UART4_RX.0.Priority=DMA_PRIORITY_LOW
Dma.UART4_RX.0.RequestParameters=Instance,Direction,PeriphInc,MemInc,PeriphDataAlignment,MemDataAlignment,Mode,Priority,FIFOMode
File.Version=6
GPIO.groupedBy=Group By Peripherals
KeepUserPlacement=false
Mcu.CPN=STM32F407VGT6
Mcu.Family=STM32F4
Mcu.IP0=DMA
Mcu.IP1=I2C2
Mcu.IP10=UART4
Mcu.IP11=USART1
Mcu.IP12=USART2
Mcu.IP13=USART3
Mcu.IP14=USART6
Mcu.IP2=NVIC
Mcu.IP3=RCC
Mcu.IP4=SYS
Mcu.IP5=TIM1
Mcu.IP6=TIM2
Mcu.IP7=TIM3
Mcu.IP8=TIM4
Mcu.IP9=TIM11
Mcu.IPNb=15
Mcu.Name=STM32F407V(E-G)Tx
Mcu.Package=LQFP100
Mcu.Pin0=PE2
Mcu.Pin1=PE3
Mcu.Pin10=PA0-WKUP
Mcu.Pin11=PA1
Mcu.Pin12=PA6
Mcu.Pin13=PA7
Mcu.Pin14=PE9
Mcu.Pin15=PE11
Mcu.Pin16=PB10
Mcu.Pin17=PB11
Mcu.Pin18=PB12
Mcu.Pin19=PB13
Mcu.Pin2=PE4
Mcu.Pin20=PB14
Mcu.Pin21=PB15
Mcu.Pin22=PD8
Mcu.Pin23=PD9
Mcu.Pin24=PD10
Mcu.Pin25=PD11
Mcu.Pin26=PD12
Mcu.Pin27=PD13
Mcu.Pin28=PD15
Mcu.Pin29=PC6
Mcu.Pin3=PE5
Mcu.Pin30=PC7
Mcu.Pin31=PA9
Mcu.Pin32=PA10
Mcu.Pin33=PA13
Mcu.Pin34=PA14
Mcu.Pin35=PC10
Mcu.Pin36=PC11
Mcu.Pin37=PC12
Mcu.Pin38=PD0
Mcu.Pin39=PD1
Mcu.Pin4=PE6
Mcu.Pin40=PD2
Mcu.Pin41=PD3
Mcu.Pin42=PD4
Mcu.Pin43=PD5
Mcu.Pin44=PD6
Mcu.Pin45=PB8
Mcu.Pin46=PB9
Mcu.Pin47=VP_SYS_VS_Systick
Mcu.Pin48=VP_TIM2_VS_ClockSourceINT
Mcu.Pin49=VP_TIM4_VS_ClockSourceINT
Mcu.Pin5=PC13-ANTI_TAMP
Mcu.Pin50=VP_TIM11_VS_ClockSourceINT
Mcu.Pin6=PC14-OSC32_IN
Mcu.Pin7=PC15-OSC32_OUT
Mcu.Pin8=PH0-OSC_IN
Mcu.Pin9=PH1-OSC_OUT
Mcu.PinsNb=51
Mcu.ThirdPartyNb=0
Mcu.UserConstants=
Mcu.UserName=STM32F407VGTx
MxCube.Version=6.12.1
MxDb.Version=DB.6.0.121
NVIC.BusFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.DMA1_Stream2_IRQn=true\:0\:0\:false\:false\:true\:false\:true\:true
NVIC.DebugMonitor_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.ForceEnableDMAVector=true
NVIC.HardFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.MemoryManagement_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.NonMaskableInt_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PendSV_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.PriorityGroup=NVIC_PRIORITYGROUP_4
NVIC.SVCall_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
NVIC.SysTick_IRQn=true\:15\:0\:false\:false\:true\:false\:true\:false
NVIC.TIM1_TRG_COM_TIM11_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.TIM2_IRQn=true\:0\:0\:true\:false\:true\:true\:true\:true
NVIC.TIM4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UART4_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART1_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART2_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART3_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.USART6_IRQn=true\:0\:0\:false\:false\:true\:true\:true\:true
NVIC.UsageFault_IRQn=true\:0\:0\:false\:false\:true\:false\:false\:false
PA0-WKUP.Mode=Asynchronous
PA0-WKUP.Signal=UART4_TX
PA1.Locked=true
PA1.Mode=Asynchronous
PA1.Signal=UART4_RX
PA10.Mode=Asynchronous
PA10.Signal=USART1_RX
PA13.Mode=Serial_Wire
PA13.Signal=SYS_JTMS-SWDIO
PA14.Mode=Serial_Wire
PA14.Signal=SYS_JTCK-SWCLK
PA6.Locked=true
PA6.Signal=S_TIM3_CH1
PA7.Signal=S_TIM3_CH2
PA9.Mode=Asynchronous
PA9.Signal=USART1_TX
PB10.Locked=true
PB10.Mode=I2C
PB10.Signal=I2C2_SCL
PB11.Locked=true
PB11.Mode=I2C
PB11.Signal=I2C2_SDA
PB12.GPIOParameters=GPIO_PuPd,GPIO_Label
PB12.GPIO_Label=LED1
PB12.GPIO_PuPd=GPIO_PULLUP
PB12.Locked=true
PB12.Signal=GPIO_Output
PB13.GPIOParameters=GPIO_PuPd,GPIO_Label
PB13.GPIO_Label=LED2
PB13.GPIO_PuPd=GPIO_PULLUP
PB13.Locked=true
PB13.Signal=GPIO_Output
PB14.GPIOParameters=GPIO_PuPd,GPIO_Label
PB14.GPIO_Label=LED3
PB14.GPIO_PuPd=GPIO_PULLUP
PB14.Locked=true
PB14.Signal=GPIO_Output
PB15.GPIOParameters=GPIO_PuPd,GPIO_Label
PB15.GPIO_Label=LED4
PB15.GPIO_PuPd=GPIO_PULLUP
PB15.Locked=true
PB15.Signal=GPIO_Output
PB8.GPIOParameters=GPIO_Label
PB8.GPIO_Label=TRIG
PB8.Locked=true
PB8.Signal=GPIO_Output
PB9.GPIOParameters=GPIO_Label
PB9.GPIO_Label=ECHO
PB9.Locked=true
PB9.Signal=S_TIM11_CH1
PC10.Mode=Asynchronous
PC10.Signal=USART3_TX
PC11.Mode=Asynchronous
PC11.Signal=USART3_RX
PC12.GPIOParameters=GPIO_PuPd,GPIO_Label
PC12.GPIO_Label=KEY1
PC12.GPIO_PuPd=GPIO_PULLUP
PC12.Locked=true
PC12.Signal=GPIO_Input
PC13-ANTI_TAMP.GPIOParameters=GPIO_PuPd,GPIO_Label
PC13-ANTI_TAMP.GPIO_Label=HD6
PC13-ANTI_TAMP.GPIO_PuPd=GPIO_PULLUP
PC13-ANTI_TAMP.Locked=true
PC13-ANTI_TAMP.Signal=GPIO_Input
PC14-OSC32_IN.Mode=LSE-External-Oscillator
PC14-OSC32_IN.Signal=RCC_OSC32_IN
PC15-OSC32_OUT.Mode=LSE-External-Oscillator
PC15-OSC32_OUT.Signal=RCC_OSC32_OUT
PC6.Locked=true
PC6.Mode=Asynchronous
PC6.Signal=USART6_TX
PC7.Locked=true
PC7.Mode=Asynchronous
PC7.Signal=USART6_RX
PD0.GPIOParameters=GPIO_PuPd,GPIO_Label
PD0.GPIO_Label=KEY2
PD0.GPIO_PuPd=GPIO_PULLUP
PD0.Locked=true
PD0.Signal=GPIO_Input
PD1.GPIOParameters=GPIO_PuPd,GPIO_Label
PD1.GPIO_Label=KEY3
PD1.GPIO_PuPd=GPIO_PULLUP
PD1.Locked=true
PD1.Signal=GPIO_Input
PD10.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PD10.GPIO_Label=AIN2
PD10.GPIO_PuPd=GPIO_PULLDOWN
PD10.Locked=true
PD10.PinState=GPIO_PIN_RESET
PD10.Signal=GPIO_Output
PD11.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PD11.GPIO_Label=AIN1
PD11.GPIO_PuPd=GPIO_PULLDOWN
PD11.Locked=true
PD11.PinState=GPIO_PIN_RESET
PD11.Signal=GPIO_Output
PD12.GPIOParameters=GPIO_Label
PD12.GPIO_Label=PWMB
PD12.Locked=true
PD12.Signal=S_TIM4_CH1
PD13.GPIOParameters=GPIO_Label
PD13.GPIO_Label=PWMA
PD13.Locked=true
PD13.Signal=S_TIM4_CH2
PD15.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PD15.GPIO_Label=BEEP
PD15.GPIO_PuPd=GPIO_PULLUP
PD15.Locked=true
PD15.PinState=GPIO_PIN_SET
PD15.Signal=GPIO_Output
PD2.GPIOParameters=GPIO_PuPd,GPIO_Label
PD2.GPIO_Label=KEY4
PD2.GPIO_PuPd=GPIO_PULLUP
PD2.Locked=true
PD2.Signal=GPIO_Input
PD3.GPIOParameters=GPIO_PuPd,GPIO_Label
PD3.GPIO_Label=KEY5
PD3.GPIO_PuPd=GPIO_PULLUP
PD3.Locked=true
PD3.Signal=GPIO_Input
PD4.GPIOParameters=GPIO_PuPd,GPIO_Label
PD4.GPIO_Label=KEY6
PD4.GPIO_PuPd=GPIO_PULLUP
PD4.Locked=true
PD4.Signal=GPIO_Input
PD5.Locked=true
PD5.Mode=Asynchronous
PD5.Signal=USART2_TX
PD6.Locked=true
PD6.Mode=Asynchronous
PD6.Signal=USART2_RX
PD8.GPIOParameters=PinState,GPIO_PuPd,GPIO_Label
PD8.GPIO_Label=BIN2
PD8.GPIO_PuPd=GPIO_PULLDOWN
PD8.Locked=true
PD8.PinState=GPIO_PIN_RESET
PD8.Signal=GPIO_Output
PD9.GPIOParameters=GPIO_PuPd,GPIO_Label
PD9.GPIO_Label=BIN1
PD9.GPIO_PuPd=GPIO_PULLDOWN
PD9.Locked=true
PD9.Signal=GPIO_Output
PE11.Signal=S_TIM1_CH2
PE2.GPIOParameters=GPIO_PuPd,GPIO_Label
PE2.GPIO_Label=HD1
PE2.GPIO_PuPd=GPIO_PULLUP
PE2.Locked=true
PE2.Signal=GPIO_Input
PE3.GPIOParameters=GPIO_PuPd,GPIO_Label
PE3.GPIO_Label=HD2
PE3.GPIO_PuPd=GPIO_PULLUP
PE3.Locked=true
PE3.Signal=GPIO_Input
PE4.GPIOParameters=GPIO_PuPd,GPIO_Label
PE4.GPIO_Label=HD3
PE4.GPIO_PuPd=GPIO_PULLUP
PE4.Locked=true
PE4.Signal=GPIO_Input
PE5.GPIOParameters=GPIO_PuPd,GPIO_Label
PE5.GPIO_Label=HD4
PE5.GPIO_PuPd=GPIO_PULLUP
PE5.Locked=true
PE5.Signal=GPIO_Input
PE6.GPIOParameters=GPIO_PuPd,GPIO_Label
PE6.GPIO_Label=HD5
PE6.GPIO_PuPd=GPIO_PULLUP
PE6.Locked=true
PE6.Signal=GPIO_Input
PE9.Locked=true
PE9.Signal=S_TIM1_CH1
PH0-OSC_IN.Mode=HSE-External-Oscillator
PH0-OSC_IN.Signal=RCC_OSC_IN
PH1-OSC_OUT.Mode=HSE-External-Oscillator
PH1-OSC_OUT.Signal=RCC_OSC_OUT
PinOutPanel.RotationAngle=0
ProjectManager.AskForMigrate=true
ProjectManager.BackupPrevious=false
ProjectManager.CompilerOptimize=6
ProjectManager.ComputerToolchain=false
ProjectManager.CoupleFile=true
ProjectManager.CustomerFirmwarePackage=
ProjectManager.DefaultFWLocation=true
ProjectManager.DeletePrevious=true
ProjectManager.DeviceId=STM32F407VGTx
ProjectManager.FirmwarePackage=STM32Cube FW_F4 V1.28.2
ProjectManager.FreePins=false
ProjectManager.HalAssertFull=false
ProjectManager.HeapSize=0x200
ProjectManager.KeepUserCode=true
ProjectManager.LastFirmware=true
ProjectManager.LibraryCopy=1
ProjectManager.MainLocation=Core/Src
ProjectManager.NoMain=false
ProjectManager.PreviousToolchain=
ProjectManager.ProjectBuild=false
ProjectManager.ProjectFileName=driver.ioc
ProjectManager.ProjectName=driver
ProjectManager.ProjectStructure=
ProjectManager.RegisterCallBack=
ProjectManager.StackSize=0x400
ProjectManager.TargetToolchain=MDK-ARM V5.32
ProjectManager.ToolChainLocation=
ProjectManager.UAScriptAfterPath=
ProjectManager.UAScriptBeforePath=
ProjectManager.UnderRoot=false
ProjectManager.functionlistsort=1-SystemClock_Config-RCC-false-HAL-false,2-MX_GPIO_Init-GPIO-false-HAL-true,3-MX_DMA_Init-DMA-false-HAL-true,4-MX_TIM4_Init-TIM4-false-HAL-true,5-MX_TIM3_Init-TIM3-false-HAL-true,6-MX_TIM2_Init-TIM2-false-HAL-true,7-MX_TIM1_Init-TIM1-false-HAL-true,8-MX_USART6_UART_Init-USART6-false-HAL-true,9-MX_TIM11_Init-TIM11-false-HAL-true,10-MX_USART1_UART_Init-USART1-false-HAL-true,11-MX_USART2_UART_Init-USART2-false-HAL-true,12-MX_I2C2_Init-I2C2-false-HAL-true,13-MX_USART3_UART_Init-USART3-false-HAL-true,14-MX_UART4_Init-UART4-false-HAL-true
RCC.48MHZClocksFreq_Value=84000000
RCC.AHBFreq_Value=168000000
RCC.APB1CLKDivider=RCC_HCLK_DIV4
RCC.APB1Freq_Value=42000000
RCC.APB1TimFreq_Value=84000000
RCC.APB2CLKDivider=RCC_HCLK_DIV2
RCC.APB2Freq_Value=84000000
RCC.APB2TimFreq_Value=168000000
RCC.CortexFreq_Value=168000000
RCC.EthernetFreq_Value=168000000
RCC.FCLKCortexFreq_Value=168000000
RCC.FamilyName=M
RCC.HCLKFreq_Value=168000000
RCC.HSE_VALUE=8000000
RCC.HSI_VALUE=16000000
RCC.I2SClocksFreq_Value=192000000
RCC.IPParameters=48MHZClocksFreq_Value,AHBFreq_Value,APB1CLKDivider,APB1Freq_Value,APB1TimFreq_Value,APB2CLKDivider,APB2Freq_Value,APB2TimFreq_Value,CortexFreq_Value,EthernetFreq_Value,FCLKCortexFreq_Value,FamilyName,HCLKFreq_Value,HSE_VALUE,HSI_VALUE,I2SClocksFreq_Value,LSI_VALUE,MCO2PinFreq_Value,PLLCLKFreq_Value,PLLM,PLLN,PLLQCLKFreq_Value,PLLSourceVirtual,RCC_RTC_Clock_Source,RCC_RTC_Clock_SourceVirtual,RTCFreq_Value,RTCHSEDivFreq_Value,SYSCLKFreq_VALUE,SYSCLKSource,VCOI2SOutputFreq_Value,VCOInputFreq_Value,VCOOutputFreq_Value,VcooutputI2S
RCC.LSI_VALUE=32000
RCC.MCO2PinFreq_Value=168000000
RCC.PLLCLKFreq_Value=168000000
RCC.PLLM=4
RCC.PLLN=168
RCC.PLLQCLKFreq_Value=84000000
RCC.PLLSourceVirtual=RCC_PLLSOURCE_HSE
RCC.RCC_RTC_Clock_Source=RCC_RTCCLKSOURCE_LSI
RCC.RCC_RTC_Clock_SourceVirtual=RCC_RTCCLKSOURCE_LSI
RCC.RTCFreq_Value=32000
RCC.RTCHSEDivFreq_Value=4000000
RCC.SYSCLKFreq_VALUE=168000000
RCC.SYSCLKSource=RCC_SYSCLKSOURCE_PLLCLK
RCC.VCOI2SOutputFreq_Value=384000000
RCC.VCOInputFreq_Value=2000000
RCC.VCOOutputFreq_Value=336000000
RCC.VcooutputI2S=192000000
SH.S_TIM11_CH1.0=TIM11_CH1,Input_Capture1_from_TI1
SH.S_TIM11_CH1.ConfNb=1
SH.S_TIM1_CH1.0=TIM1_CH1,Encoder_Interface
SH.S_TIM1_CH1.ConfNb=1
SH.S_TIM1_CH2.0=TIM1_CH2,Encoder_Interface
SH.S_TIM1_CH2.ConfNb=1
SH.S_TIM3_CH1.0=TIM3_CH1,Encoder_Interface
SH.S_TIM3_CH1.ConfNb=1
SH.S_TIM3_CH2.0=TIM3_CH2,Encoder_Interface
SH.S_TIM3_CH2.ConfNb=1
SH.S_TIM4_CH1.0=TIM4_CH1,PWM Generation1 CH1
SH.S_TIM4_CH1.ConfNb=1
SH.S_TIM4_CH2.0=TIM4_CH2,PWM Generation2 CH2
SH.S_TIM4_CH2.ConfNb=1
TIM1.EncoderMode=TIM_ENCODERMODE_TI12
TIM1.IPParameters=EncoderMode
TIM11.Channel=TIM_CHANNEL_1
TIM11.ICPolarity_CH1=TIM_INPUTCHANNELPOLARITY_RISING
TIM11.IPParameters=Prescaler,Channel,ICPolarity_CH1
TIM11.Prescaler=168-1
TIM2.AutoReloadPreload=TIM_AUTORELOAD_PRELOAD_DISABLE
TIM2.IPParameters=Prescaler,Period,AutoReloadPreload
TIM2.Period=1000-1
TIM2.Prescaler=84-1
TIM3.EncoderMode=TIM_ENCODERMODE_TI12
TIM3.IPParameters=EncoderMode
TIM4.Channel-PWM\ Generation1\ CH1=TIM_CHANNEL_1
TIM4.Channel-PWM\ Generation2\ CH2=TIM_CHANNEL_2
TIM4.IPParameters=Channel-PWM Generation2 CH2,Channel-PWM Generation1 CH1,Period,Prescaler
TIM4.Period=1000-1
TIM4.Prescaler=84-1
UART4.BaudRate=9600
UART4.IPParameters=VirtualMode,BaudRate
UART4.VirtualMode=Asynchronous
USART1.IPParameters=VirtualMode
USART1.VirtualMode=VM_ASYNC
USART2.BaudRate=9600
USART2.IPParameters=VirtualMode,BaudRate
USART2.VirtualMode=VM_ASYNC
USART3.IPParameters=VirtualMode
USART3.VirtualMode=VM_ASYNC
USART6.BaudRate=115200
USART6.IPParameters=VirtualMode,BaudRate
USART6.VirtualMode=VM_ASYNC
VP_SYS_VS_Systick.Mode=SysTick
VP_SYS_VS_Systick.Signal=SYS_VS_Systick
VP_TIM11_VS_ClockSourceINT.Mode=Enable_Timer
VP_TIM11_VS_ClockSourceINT.Signal=TIM11_VS_ClockSourceINT
VP_TIM2_VS_ClockSourceINT.Mode=Internal
VP_TIM2_VS_ClockSourceINT.Signal=TIM2_VS_ClockSourceINT
VP_TIM4_VS_ClockSourceINT.Mode=Internal
VP_TIM4_VS_ClockSourceINT.Signal=TIM4_VS_ClockSourceINT
board=custom
