#include "scheduler.h"

// 任务结构体
typedef struct
{
  void (*task_func)(void); // 任务函数指针
  uint32_t rate_ms;        // 执行周期（毫秒）
  uint32_t last_run;       // 上次执行时间（初始化为 0，每次运行时刷新）
} scheduler_task_t;

// 全局变量，用于存储任务数量
uint8_t task_num;

/**
 * @brief 用户初始化函数
 * 非HAL库硬件初始化函数
 */
void System_Init()
{
  // 初始化电机
  Motor_Init();
  // 初始化编码器
  Encoder_Init();
  // 超声波模块初始化
  Hcsr04Init(&htim11, TIM_CHANNEL_1);
  button_init();
  PID_Init();
  //jy60_init();
	JY901S_t jy901s;
	JY901S_Create(&jy901s, &huart2, 1000);
  JY901S_Enable(&jy901s, 1); // 开始使能
  HAL_TIM_Base_Start_IT(&htim2);
  //  Motor_Set_Speed(&left_motor, 30);
  //	Motor_Set_Speed(&right_motor, -30);
}

// 静态任务数组，每个任务包含任务函数、执行周期（毫秒）和上次运行时间（毫秒）
// ds18b20温度传感器
static scheduler_task_t scheduler_task[] =
    {
        {Led_Task, 1, 0},
        {beep_task, 1, 0},
        //{Encoder_Task, 10, 0},
        {button_task, 1, 0},
        // {PID_Task, 10, 0},
        //{Uart_Task, 10, 0},
        // {jy60_read, 5, 0},
        {JY901S_Task, 5, 0},
        //{Gray_Task, 5, 0},
        //		{MPU6050_Task, 10, 0},
        {PrintTrackerStatus, 300, 0},
};
uint8_t input_flag = 0;          // 入圈检测状态机
uint16_t input_timer_500ms = 0;  // 入圈检测计时
uint8_t point_count = 0;         // 经过的点位计数器
uint8_t output_flag = 0;         // 入圈检测状态机
uint16_t output_timer_500ms = 0; // 入圈检测计时
uint8_t mode_switch = 0;         // 题目切换
uint8_t circle_count = 0;         // 圈数计数器
uint16_t distance = 0; // 记录小车每一段行驶的距离

uint16_t led_timer_500ms = 0;  // LED点亮计时
uint16_t beep_timer_100ms = 0; // LED点亮计时
void HAL_TIM_PeriodElapsedCallback(TIM_HandleTypeDef *htim)
{
  static uint8_t measure_timer5ms = 0;
  if (htim->Instance == TIM11)
  {
    Hcsr04TimOverflowIsr(&htim11);
  }
  if (htim->Instance == TIM2)
  {
    if (led_buf[1] == 1 && ++led_timer_500ms >= 500)
    {
      led_timer_500ms = 0;
      led_buf[1] = 0;
    }
    if (beep_status == 1 && ++beep_timer_100ms >= 100)
    {
      beep_timer_100ms = 0;
      beep_status = 0;
    }
    if (++measure_timer5ms >= 5)
    {
      measure_timer5ms = 0;
      Encoder_Task();
			distance += left_encoder.speed_cm_s;
      Gray_Task();
      PID_Task();
      // jy60_task();
    }
    // 出圈检测器
    if (Digital != 0x00)
    {
      input_flag = 1;
      if (++input_timer_500ms >= 500)
        input_timer_500ms = 800;
    }
    else if (input_flag && input_timer_500ms == 800)
    {
      input_timer_500ms = 0;
      input_flag = 0;
      point_count++;
      task_switch();
    }
    // 入圈检测器
    if (Digital == 0x00)
    {
      output_flag = 1;
      if (++output_timer_500ms >= 500)
        output_timer_500ms = 800;
    }
    else if (output_flag && output_timer_500ms == 800)
    {
      output_flag = 0;
      output_timer_500ms = 0;
      point_count++;
      task_switch();
    }
  }
}

void task_switch(void)
{
  beep_status = 1;
  led_buf[1] = 1;
  distance = 0;
  switch (mode_switch)
  {
  case 1: // 第一题：A	 ->	 B
    if (point_count == 1)
    {
      pid_running = 0;
      Motor_Stop(&left_motor);
      Motor_Stop(&right_motor);
    }
    break;
  case 2: // 第二题：A	 ->	 B	 ->	 C  ->	 D  ->	 A
    if (point_count == 1)
      pid_mode = 1;
    else if (point_count == 2)
    {
      //			pid_running = 0;
      //      Motor_Stop(&left_motor);
      //      Motor_Stop(&right_motor);
      pid_mode = 0; // 使用角度环控制
      pid_set_target(&pid_angle, -180.0f);
    }
    else if (point_count == 3)
      pid_mode = 1; // 使用循迹环控制
    else if (point_count == 4)
    {
      pid_running = 0;
      Motor_Stop(&left_motor);
      Motor_Stop(&right_motor);
    }
    break;
  case 3: // 第三题：A	 ->	 C	 ->	 B  ->	 D  ->	 A
    if (point_count == 1)
    {
      pid_mode = 1; // 使用循迹环控制
    }
    else if (point_count == 2)
    {
      pid_mode = 0; // 使用角度环控制
      pid_set_target(&pid_angle, 263.0f);
    }
    else if (point_count == 3)
    {
      pid_mode = 1; // 使用循迹环控制
    }
    else if (point_count == 4)
    {
      pid_running = 0;
      Motor_Stop(&left_motor);
      Motor_Stop(&right_motor);
    }
    break;
  case 4:
		if (point_count == 1)
    {
      pid_mode = 1; // 使用循迹环控制
    }
    else if (point_count == 2)
    {
      pid_mode = 0; // 使用角度环控制
      pid_set_target(&pid_angle, 263.0f + ((circle_count <= 1) ? (2.4 * circle_count) : (4.3 * circle_count)));
    }
    else if (point_count == 3)
    {
      pid_mode = 1; // 使用循迹环控制
    }
    else if (point_count == 4)
    {
			if(++circle_count >=4)
			{
				pid_running = 0;
				Motor_Stop(&left_motor);
				Motor_Stop(&right_motor);
			}
			point_count = 0;
			pid_mode = 0; // 使用角度环控制
			pid_set_target(&pid_angle, 0.0f + (5 * circle_count));
    }
    break;
  }
  pid_reset(&pid_line);
  pid_reset(&pid_angle);
}

/**
 * @brief 调度器初始化函数
 * 计算任务数组的元素个数，并将结果存储在 task_num 中
 */
void Scheduler_Init(void)
{
  System_Init();
  // 计算任务数组的元素个数，并将结果存储在 task_num 中
  task_num = sizeof(scheduler_task) / sizeof(scheduler_task_t); // 数组大小 / 数组成员大小 = 数组元素个数
}

/**
 * @brief 调度器运行函数
 * 遍历任务数组，检查是否有任务需要执行。如果当前时间已经超过任务的执行周期，则执行该任务并更新上次运行时间
 */
void Scheduler_Run(void)
{
  // 遍历任务数组中的所有任务
  for (uint8_t i = 0; i < task_num; i++)
  {
    // 获取当前的系统时间（毫秒）
    uint32_t now_time = HAL_GetTick();

    // 检查当前时间是否达到任务的执行时间
    if (now_time >= scheduler_task[i].rate_ms + scheduler_task[i].last_run)
    {
      // 更新任务的上次运行时间为当前时间
      scheduler_task[i].last_run = now_time;

      // 执行任务函数
      scheduler_task[i].task_func();
    }
  }
}
