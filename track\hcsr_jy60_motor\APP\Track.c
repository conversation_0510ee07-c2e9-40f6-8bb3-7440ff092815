#include "Track.h"
#include "hardware_iic.h"
#include "gw_grayscale_sensor.h"

// 定义红外循迹传感器变量
#if TRACK_MODE_TYPE == TRACK_MODE_IO
uint8_t sensor1, sensor2, sensor3, sensor4, sensor5, sensor6;
const float weights[6] = {-3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f};
#elif TRACK_MODE_TYPE == TRACK_MODE_HARDWARE_IIC
const float weights[8] = {-4.0f, -3.0f, -2.0f, -1.0f, 1.0f, 2.0f, 3.0f, 4.0f};
uint8_t Digital;
#endif
float g_line_position_error; // 循迹误差值

// 传感器读取函数
void read_line_sensors(void)
{
#if TRACK_MODE_TYPE == TRACK_MODE_IO
    sensor1 = !HAL_GPIO_ReadPin(HD1_GPIO_Port, HD1_Pin); // 最左侧传感器
    sensor2 = !HAL_GPIO_ReadPin(HD2_GPIO_Port, HD2_Pin);
    sensor3 = !HAL_GPIO_ReadPin(HD3_GPIO_Port, HD3_Pin);
    sensor4 = !HAL_GPIO_ReadPin(HD4_GPIO_Port, HD4_Pin);
    sensor5 = !HAL_GPIO_ReadPin(HD5_GPIO_Port, HD5_Pin);
    sensor6 = !HAL_GPIO_ReadPin(HD6_GPIO_Port, HD6_Pin); // 最右侧传感器
#elif TRACK_MODE_TYPE == TRACK_MODE_HARDWARE_IIC
    Digital = ~IIC_Get_Digtal();
#endif
    // 传感器检测到黑线时输出0，白色时输出
    //    printf("Sensors: S1:%d S2:%d S3:%d S4:%d S5:%d S6:%d ",
    //           sensor1, sensor2, sensor3, sensor4, sensor5, sensor6);

    // 判断是否所有传感器都没检测到线
    //    if (!sensor1 && !sensor2 && !sensor3 && !sensor4 && !sensor5 && !sensor6)
    //    {
    //        printf(">>> ALL LOST <<<\r\n");
    //    }
    //    else
    //    {
    //        printf(">>> LINE FOUND <<<\r\n");
    //    }
}

///* 打印传感器状态的函数 */
void PrintTrackerStatus(void)
{
    my_printf(&huart4, "Line Tracker Status:\r\n");
#if TRACK_MODE_TYPE == TRACK_MODE_IO
    my_printf(&huart4, "sensor1: %d\r\n", sensor1);
    my_printf(&huart4, "sensor2: %d\r\n", sensor2);
    my_printf(&huart4, "sensor3: %d\r\n", sensor3);
    my_printf(&huart4, "sensor4: %d\r\n", sensor4);
    my_printf(&huart4, "sensor5: %d\r\n", sensor5);
    my_printf(&huart4, "sensor6: %d\r\n", sensor6);
    HAL_Delay(1000);
#elif TRACK_MODE_TYPE == TRACK_MODE_HARDWARE_IIC
    // for (uint8_t i = 0; i < 8; i++)
    //     my_printf(&huart4, "sensor%d: %d\r\n", i + 1, (Digital >> i) & 0x01);
    my_printf(&huart4, "yaw:%.2f\r\n", yaw);
    my_printf(&huart4, "pid:%d mode:%d point:%d\r\n", pid_running, mode_switch, point_count);
    // my_printf(&huart4, "Left:%.2f Right:%.2f\r\n", left_encoder.speed_cm_s, right_encoder.speed_cm_s);
#endif
}

void Gray_Task(void)
{
    int black_line_count = 0;
    float sum = 0;
    read_line_sensors();
    // PrintTrackerStatus();

#if TRACK_MODE_TYPE == TRACK_MODE_IO
    if (sensor1)
    {
        sum += weights[0];
        black_line_count++;
    }
    if (sensor2)
    {
        sum += weights[1];
        black_line_count++;
    }
    if (sensor3)
    {
        sum += weights[2];
        black_line_count++;
    }
    if (sensor4)
    {
        sum += weights[3];
        black_line_count++;
    }
    if (sensor5)
    {
        sum += weights[4];
        black_line_count++;
    }
    if (sensor6)
    {
        sum += weights[5];
        black_line_count++;
    }
#elif TRACK_MODE_TYPE == TRACK_MODE_HARDWARE_IIC
    for (uint8_t i = 0; i < 8; i++)
    {
        if ((Digital >> i) & 0x01)
        {
            sum += weights[i];
            black_line_count++;
        }
    }
#endif
    // 1. 计算当前位置偏差
    if (black_line_count > 0)
        g_line_position_error = sum / (float)black_line_count;
}

// 主循迹函数
// void line_following_task(void)
// {
//     // 1. 读取传感器数据
//     read_line_sensors();

//     // 2. 处理特殊状态
//     // 情况1: 所有传感器都看不到线(完全丢失线路)
//     printf("Checking sensors: %d %d %d %d %d %d\r\n", sensor1, sensor2, sensor3, sensor4, sensor5, sensor6);

//     // 临时强制进入丢失线路模式进行PID测试
//     if (1) // 强制条件，原条件：()
//     {
//         printf(">>> ENTERING LOST LINE MODE <<<\r\n");
//         // 使用PID控制保持直行，避免偏移
//         static float lost_line_speed = BASE_SPEED * 0.7;

//         printf("Setting PID targets to: %.1f\r\n", lost_line_speed);

//         // 设置相同的速度目标，让速度PID自动修正差异

// 				pid_line_follow();
//         // 调试输出PID状态
//         debug_pid_status();

//         // 不跳过PID计算，让闭环控制保持直线
//         // 注释掉return，让后续PID控制继续执行
//         // return;
//     }
//     else
//     {
//         printf(">>> LINE DETECTED - NORMAL PID <<<\r\n");
//     }

//     //    // 情况2: 所有传感器都检测到线(十字交叉口或横线)
//     //    if(!sensor1 && !sensor2 && !sensor3 && !sensor4 && !sensor5 && !sensor6) {
//     //        // 直行通过交叉口
//     //        motor_left = BASE_SPEED;
//     //        motor_right = BASE_SPEED;
//     //				run(motor_right,motor_left);
//     //        return;  // 跳过PID计算
//     //    }

//     // 情况2: 检测到线路，执行正常PID循迹
//     if (sensor1 || sensor2 || sensor3 || sensor4 || sensor5 || sensor6)
//     {
//         // 执行PID循迹控制
//         stop();

//         return; // 已经执行了电机控制，直接返回
//     }

//     //    // 情况3: 急左转弯(只有最左侧传感器检测到线)
//     //    if(!sensor1 && sensor2 && sensor3 && sensor4 && sensor5 && sensor6) {
//     //        // 激进左  //     //        motor_left = BASE_SPEED * 0.3;
//     //        motor_right = BASE_SPEED * 1.7;
//     //				run(motor_right,motor_left);
//     //        return;  // 跳过PID计算
//     //    }
//     //
//     //    // 情况4: 急右转弯(只有最右侧传感器检测到线)
//     //    if(sensor1 && sensor2 && sensor3 && sensor4 && sensor5 && !sensor6) {
//     //        // 激进右转
//     //        motor_left = BASE_SPEED * 1.7;
//     //        motor_right = BASE_SPEED * 0.3;
//     //				run(motor_right,motor_left);
//     //        return;  // 跳过PID计算
//     //    }
//     //
//     //    // 情况5: 左T型路口(左侧三个传感器检测到线)
//     //    if(!sensor1 && !sensor2 && !sensor3 && sensor4 && sensor5 && sensor6) {
//     //        // 执行左转
//     //        motor_left = BASE_SPEED * 0.4;
//     //        motor_right = BASE_SPEED * 1.2;
//     //				run(motor_right,motor_left);
//     //        HAL_Delay(300);  // 保持转向一段时间
//     //        return;
//     //    }
//     //
//     //    // 情况6: 右T型路口(右侧三个传感器检测到线)
//     //    if(sensor1 && sensor2 && sensor3 && !sensor4 && !sensor5 && !sensor6) {
//     //        // 执行右转
//     //        motor_left = BASE_SPEED * 1.2;
//     //        motor_right = BASE_SPEED * 0.4;
//     //				run(motor_right,motor_left);
//     //        HAL_Delay(300);  // 保持转向一段时间
//     //        return;
//     //    }
//     //
//     //    // 3. 如果没有特殊状态，执行正常的PID循迹
//     //    pid_line_follow();

//     // 4. 应用电机控制
//     // 使用PWM驱动电机

//     // __HAL_TIM_SET_COMPARE(&htimX, TIM_CHANNEL_X, motor_left);   // 左电机PWM
//     // __HAL_TIM_SET_COMPARE(&htimX, TIM_CHANNEL_Y, motor_right);  // 右电机PWM
// }
