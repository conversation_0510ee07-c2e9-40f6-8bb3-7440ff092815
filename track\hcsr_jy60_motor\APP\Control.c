#include "Control.h"

/* PID 控制器实例 */
PID_T pid_speed_left;  // 左轮速度环
PID_T pid_speed_right; // 右轮速度环
PID_T pid_line;		   // 循迹环
PID_T pid_angle;	   // 角度环

// 增量式PID：P-稳定性，I-响应性，D-准确性
int basic_speed = 50; // 基础速度

PidParams_t pid_params_speed_left = {
	.kp = 18.0f, // 增量式PID的P参数
	.ki = 0.1f,	 // 增量式PID的I参数
	.kd = 1.9f,	 // 增量式PID的D参数，不宜过大
	.out_min = -999.0f,
	.out_max = 999.0f,
};

PidParams_t pid_params_speed_right = {
	.kp = 18.0f, // 增量式PID的P参数
	.ki = 0.1f,	 // 增量式PID的I参数  不适宜给太大 因为他会积累误差
	.kd = 1.9f,	 // 增量式PID的D参数，不宜过大
	.out_min = -999.0f,
	.out_max = 999.0f,
};
// 巡线控制PID (位置式)
PidParams_t pid_params_line = {
	.kp = 8.7f,
	.ki = 0.0000f,
	.kd = 6.00f,
	.out_min = -999.0f,
	.out_max = 999.0f,
};
// 角度环（偏航角）
PidParams_t pid_params_angle = {
	.kp = 1.2f,
	.ki = 0.00001f,
	.kd = 6.800f,
	.out_min = -999.0f,
	.out_max = 999.0f,
};

// 初始化PID控制器
void PID_Init(void)
{
	// 初始化左轮速度PID控制器
	pid_init(&pid_speed_left,
			 pid_params_speed_left.kp, pid_params_speed_left.ki, pid_params_speed_left.kd,
			 0.0f, pid_params_speed_left.out_max);

	// 初始化右轮速度PID控制器
	pid_init(&pid_speed_right,
			 pid_params_speed_right.kp, pid_params_speed_right.ki, pid_params_speed_right.kd,
			 0.0f, pid_params_speed_right.out_max);

	// 初始化直线行驶PID控制器
	pid_init(&pid_line,
			 pid_params_line.kp, pid_params_line.ki, pid_params_line.kd,
			 0.0f, pid_params_line.out_max);

	pid_init(&pid_angle,
			 pid_params_angle.kp, pid_params_angle.ki, pid_params_angle.kd,
			 0.0f, pid_params_angle.out_max);

	// 设置左轮速度PID控制器的目标值
	pid_set_target(&pid_speed_left, basic_speed);
	// 设置右轮速度PID控制器的目标值
	pid_set_target(&pid_speed_right, basic_speed);
	pid_set_target(&pid_line, 0);
	pid_set_target(&pid_angle, 0);
}

// 低通滤波器系数 (Low-pass filter coefficient 'alpha')
// alpha 越小, 滤波效果越强, 但延迟越大。建议从 0.1 到 0.5 之间开始尝试。
#define SPEED_FILTER_ALPHA_LEFT 0.15f
#define SPEED_FILTER_ALPHA_RIGHT 0.15f

// 用于存储滤波后速度的变量
static float filtered_speed_left = 0.0f;
static float filtered_speed_right = 0.0f;

bool pid_running = false;	// PID 控制使能开关
uint8_t pid_mode = 0;		// PID 控制模式 0 角度环 1 寻迹环
void Line_PID_control(void) // 循迹环控制
{
	int line_pid_output = 0;
	line_pid_output = pid_calculate_incremental(&pid_line, g_line_position_error);
	line_pid_output = pid_constrain(line_pid_output, pid_params_line.out_min, pid_params_line.out_max);
	// pid_set_target(&pid_speed_left, basic_speed + 1.2 * line_pid_output);
	// pid_set_target(&pid_speed_right, basic_speed - 0.8 * line_pid_output);
	pid_set_target(&pid_speed_left, basic_speed + line_pid_output);
	pid_set_target(&pid_speed_right, basic_speed - line_pid_output);
}

void Angle_PID_control(void) // 角度环控制
{
	int angle_pid_output = 0;
	angle_pid_output = pid_calculate_incremental(&pid_angle, yaw);
	angle_pid_output = pid_constrain(angle_pid_output, pid_params_angle.out_min, pid_params_angle.out_max);
	pid_set_target(&pid_speed_left, basic_speed + 0.8 * angle_pid_output);
	pid_set_target(&pid_speed_right, basic_speed - 0.8 * angle_pid_output);
	// my_printf(&huart4, "Target:%.1f Yaw:%.1f Error:%.1f Out:%d\r\n", pid_angle.target, yaw, pid_angle.target - yaw, angle_pid_output);
}
void PID_Task(void)
{
	if (pid_running == false)
		return;

	float output_speed_left, output_speed_right;
	 if (pid_mode == 0)
	 {
		 if(distance > 20000) // 超过指定距离时减速
        basic_speed = 35;
      else
        basic_speed = 50;
		 Angle_PID_control();
	 }
	 else if (pid_mode == 1)
	 {
		 basic_speed = 40;
		 Line_PID_control();
	 }

	// filtered = alpha * raw + (1 - alpha) * previous_filtered
	filtered_speed_left = SPEED_FILTER_ALPHA_LEFT * left_encoder.speed_cm_s + (1.0f - SPEED_FILTER_ALPHA_LEFT) * filtered_speed_left;
	filtered_speed_right = SPEED_FILTER_ALPHA_RIGHT * right_encoder.speed_cm_s + (1.0f - SPEED_FILTER_ALPHA_RIGHT) * filtered_speed_right;

	output_speed_left = pid_calculate_incremental(&pid_speed_left, filtered_speed_left);
	output_speed_right = pid_calculate_incremental(&pid_speed_right, filtered_speed_right);
	//	 output_yaw =  pid_calculate_incremental(&pid_yaw, yaw); //-> PID计算 YAW -> 反馈
	//	 pid_set_target(&pid_speed_left,v + output_yaw);// -> 作用对象
	//	 pid_set_target(&pid_speed_right,v - output_yaw);
	output_speed_left = pid_constrain(output_speed_left, pid_params_speed_left.out_min, pid_params_speed_left.out_max);
	output_speed_right = pid_constrain(output_speed_right, pid_params_speed_right.out_min, pid_params_speed_right.out_max);
	// 设置电机速度
	// Motor_SetSpeed(&left_motor, output_left);
	// Motor_SetSpeed(&right_motor, output_right);
	Motor_Set_Speed(&left_motor, output_speed_left);
	Motor_Set_Speed(&right_motor, output_speed_right);
	// 可以打开
	//	 my_printf(&huart4, "{left_filtered}%.2f,%.2f,%.2f\r\n", pid_speed_left.target, filtered_speed_left, output_left);
	//	 my_printf(&huart4, "{right_filtered}%.2f,%.2f,%.2f\r\n", pid_speed_right.target, filtered_speed_right, output_right);
}

// 测试函数：设置相同的PWM值测试左右轮是否同步
void Test_Motor_Sync(uint16_t pwm_value)
{
	Motor_Set_Speed(&left_motor, pwm_value);
	Motor_Set_Speed(&right_motor, pwm_value);
	my_printf(&huart4, "{test_sync}PWM:%d,L_Speed:%.2f,R_Speed:%.2f\r\n",
			  pwm_value, left_encoder.speed_cm_s, right_encoder.speed_cm_s);
}

// void car_go(int32_t target_location_cm)
// {
// 	float Car_location;

// 	g_fTargetJourney = target_location_cm; // 防止长时间PID控制用

// 	Car_location = target_location_cm * (Reduction_ratio * Frequency_Divide) / (2 * WheelR * 3.1416);
// 	// printf("Car_location = %f\n", Car_location);
// 	set_pid_target(&g_tRightLocalPID, Car_location);
// 	set_pid_target(&g_tLeftLocalPID, Car_location);

// 	//	Location_Speed_control();

// 	g_lMotor_Lef_PulseSigma = 0; // 这个一定要清0，不然会一直累加
// 	g_lMotor_Rig_PulseSigma = 0;
// }
