/**
 ******************************************************************************
 * @file    jy901s_driver.h
 * @brief   JY901S 九轴 IMU 驱动库头文件
 * <AUTHOR>
 * @date    2025-07-19
 ******************************************************************************
 * @attention
 *
 * 本库专为 JY901S 九轴传感器设计
 * 支持加速度、角速度、角度数据读取，提供低耦合接口，
 * 用户只需提供串口收发接口。
 *
 ******************************************************************************
 */

#ifndef __JY901S_DRIVER_H__
#define __JY901S_DRIVER_H__

#ifdef __cplusplus
extern "C"
{
#endif

/* Includes ------------------------------------------------------------------*/
#include "mydefine.h"
#include "usart.h"
#include <stdarg.h>

    /* 用户可自定义 printf 实现 --------------------------------------------*/
    // 用户可自定义串口 printf
    extern int my_printf(UART_HandleTypeDef *huart, const char *format, ...);

    /* Exported types ------------------------------------------------------------*/

    /**
     * @brief JY901S 状态枚举
     */
    typedef enum
    {
        JY901S_STATE_IDLE = 0,   // 空闲
        JY901S_STATE_RECEIVING,  // 接收中
        JY901S_STATE_DATA_READY, // 数据就绪
        JY901S_STATE_ERROR       // 错误
    } JY901S_State_t;

    /**
     * @brief JY901S 硬件配置结构体
     */
    typedef struct
    {
        UART_HandleTypeDef *huart; // 串口句柄
        uint32_t timeout_ms;       // 超时时间
    } JY901S_HW_t;

    /**
     * @brief JY901S 数据结构体
     */
    typedef struct
    {
        float acc_x;        // 加速度 X (g)
        float acc_y;        // 加速度 Y (g)
        float acc_z;        // 加速度 Z (g)
        float gyro_x;       // 角速度 X (°/s)
        float gyro_y;       // 角速度 Y (°/s)
        float gyro_z;       // 角速度 Z (°/s)
        float yaw;          // 偏航角 (°)
        float pitch;        // 俯仰角 (°)
        float roll;         // 横滚角 (°)
        uint32_t timestamp; // 时间戳
        uint8_t data_valid; // 数据有效标志
    } JY901S_Data_t;

    /**
     * @brief JY901S 驱动实体结构体
     */
    typedef struct
    {
        JY901S_HW_t hw;        // 硬件配置
        JY901S_Data_t data;    // 传感器数据
        JY901S_State_t state;  // 当前状态
        uint8_t enable;        // 使能标志
        uint8_t rx_buffer[32]; // 接收缓冲区
        uint8_t rx_index;      // 接收索引
    } JY901S_t;

/* Exported constants --------------------------------------------------------*/
#define JY901S_PACKET_SIZE 11  // JY901S 数据包固定长度
#define JY901S_TIMEOUT_MS 1000 // 默认超时时间
#define JY901S_BUFFER_SIZE 32  // 接收缓冲区大小

/* JY901S 协议常量 */
#define JY901S_HEADER 0x55     // 数据包头
#define JY901S_TYPE_ACC 0x51   // 加速度数据类型
#define JY901S_TYPE_GYRO 0x52  // 角速度数据类型
#define JY901S_TYPE_ANGLE 0x53 // 角度数据类型

#define JY901S_DMA_BUFFER_SIZE 32
    /* Exported functions prototypes ---------------------------------------------*/

    /**
     * @brief 创建 JY901S 实体
     * @param imu: JY901S 实体指针
     * @param huart: 串口句柄
     * @param timeout_ms: 超时时间
     * @retval 0: 成功, -1: 参数错误
     */
    int8_t JY901S_Create(JY901S_t *imu, UART_HandleTypeDef *huart, uint32_t timeout_ms);

    /**
     * @brief 处理接收数据缓冲区
     * @param imu: JY901S 实体指针
     * @param buffer: 接收数据缓冲区
     * @param length: 数据长度
     * @retval 0: 成功, -1: 参数错误
     * @note 用户在串口接收中断或 DMA 完成回调中调用此函数
     */
    int8_t JY901S_ProcessBuffer(JY901S_t *imu, uint8_t *buffer, uint16_t length);

    /**
     * @brief 获取偏航角
     * @param imu: JY901S 实体指针
     * @retval 偏航角值 (°)，无效时返回 0.0
     */
    float JY901S_GetYaw(JY901S_t *imu);

    /**
     * @brief 获取俯仰角
     * @param imu: JY901S 实体指针
     * @retval 俯仰角值 (°)，无效时返回 0.0
     */
    float JY901S_GetPitch(JY901S_t *imu);

    /**
     * @brief 获取横滚角
     * @param imu: JY901S 实体指针
     * @retval 横滚角值 (°)，无效时返回 0.0
     */
    float JY901S_GetRoll(JY901S_t *imu);

    /**
     * @brief 获取完整数据
     * @param imu: JY901S 实体指针
     * @retval 数据结构体指针，无效时返回 NULL
     */
    JY901S_Data_t *JY901S_GetData(JY901S_t *imu);

    /**
     * @brief 获取 JY901S 状态
     * @param imu: JY901S 实体指针
     * @retval 当前状态
     */
    JY901S_State_t JY901S_GetState(JY901S_t *imu);

    /**
     * @brief 使能/失能 JY901S
     * @param imu: JY901S 实体指针
     * @param enable: 1-使能, 0-失能
     * @retval 0: 成功, -1: 参数错误
     */
    int8_t JY901S_Enable(JY901S_t *imu, uint8_t enable);
    void JY901S_Task(void);

#ifdef __cplusplus
}
#endif

#endif /* __JY901S_DRIVER_H__ */
